#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الإصلاحات المطبقة على وكيل أخبار الألعاب
"""

import asyncio
import sys
import os
import logging
from datetime import datetime

# إضافة المجلد الحالي إلى المسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_fixes.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

async def test_imports():
    """اختبار استيراد جميع الوحدات"""
    logger.info("🧪 اختبار استيراد الوحدات...")
    
    try:
        # اختبار الوحدات الأساسية
        from modules.publisher import TelegramPublisher
        logger.info("✅ تم استيراد TelegramPublisher بنجاح")
        
        from modules.advanced_youtube_analyzer import AdvancedYouTubeAnalyzer
        logger.info("✅ تم استيراد AdvancedYouTubeAnalyzer بنجاح")
        
        from modules.content_generator import ContentGenerator
        logger.info("✅ تم استيراد ContentGenerator بنجاح")
        
        from config.settings import BotConfig
        logger.info("✅ تم استيراد BotConfig بنجاح")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ فشل في استيراد الوحدات: {e}")
        return False

async def test_telegram_publisher():
    """اختبار منشر Telegram"""
    logger.info("🧪 اختبار منشر Telegram...")
    
    try:
        from modules.publisher import TelegramPublisher
        
        # إنشاء مثال للاختبار مع معاملات وهمية
        publisher = TelegramPublisher("test_token", "test_channel")
        
        # اختبار تنظيف النص
        test_text = "هذا نص تجريبي مع رموز خاصة * _ [ ] ( ) `"
        cleaned_text = publisher._clean_text_for_telegram(test_text)
        logger.info(f"✅ تنظيف النص: '{test_text}' -> '{cleaned_text}'")
        
        # اختبار تنسيق الرسائل
        test_article = {
            'title': 'خبر ألعاب تجريبي',
            'content': 'هذا محتوى تجريبي لاختبار تنسيق الرسائل',
            'meta_description': 'وصف تجريبي',
            'dialect': 'egyptian'
        }
        
        # اختبار تنسيق الملخص القصير
        short_summary = publisher._format_short_summary(test_article)
        logger.info("✅ تم تنسيق الملخص القصير بنجاح")
        
        # التحقق من عدم وجود "ماين كرافت" في الرسالة
        if "ماين كرافت" in short_summary:
            logger.warning("⚠️ لا يزال هناك إشارة لماين كرافت في الرسالة")
        else:
            logger.info("✅ تم إزالة الإشارات لماين كرافت بنجاح")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ فشل في اختبار منشر Telegram: {e}")
        return False

async def test_youtube_analyzer():
    """اختبار محلل YouTube"""
    logger.info("🧪 اختبار محلل YouTube...")
    
    try:
        from modules.advanced_youtube_analyzer import AdvancedYouTubeAnalyzer
        
        # إنشاء مثال للاختبار
        analyzer = AdvancedYouTubeAnalyzer()
        
        # اختبار القنوات المحددة
        logger.info(f"✅ تم تحميل {len(analyzer.priority_channels)} قناة")
        
        # التحقق من تنوع القنوات
        languages = set(channel['language'] for channel in analyzer.priority_channels)
        logger.info(f"✅ اللغات المدعومة: {languages}")
        
        # اختبار الكلمات المفتاحية
        ar_keywords = analyzer.gaming_keywords.get('ar', [])
        en_keywords = analyzer.gaming_keywords.get('en', [])
        logger.info(f"✅ كلمات مفتاحية عربية: {len(ar_keywords)}")
        logger.info(f"✅ كلمات مفتاحية إنجليزية: {len(en_keywords)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ فشل في اختبار محلل YouTube: {e}")
        return False

async def test_content_generator():
    """اختبار مولد المحتوى"""
    logger.info("🧪 اختبار مولد المحتوى...")
    
    try:
        from modules.content_generator import ContentGenerator
        
        # إنشاء مثال للاختبار (بدون استدعاء APIs فعلية)
        generator = ContentGenerator()
        logger.info("✅ تم إنشاء مولد المحتوى بنجاح")
        
        # اختبار استخراج الكلمات المفتاحية للألعاب
        test_content = {
            'title': 'أحدث أخبار PlayStation 5 و Xbox Series X',
            'content': 'تحديثات جديدة للألعاب على منصات الجيل الجديد'
        }
        
        game_queries = generator._extract_game_queries(test_content)
        logger.info(f"✅ تم استخراج {len(game_queries)} استعلام للألعاب")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ فشل في اختبار مولد المحتوى: {e}")
        return False

async def main():
    """الدالة الرئيسية للاختبار"""
    logger.info("🚀 بدء اختبار الإصلاحات...")
    
    tests = [
        ("استيراد الوحدات", test_imports),
        ("منشر Telegram", test_telegram_publisher),
        ("محلل YouTube", test_youtube_analyzer),
        ("مولد المحتوى", test_content_generator)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"🧪 اختبار: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = await test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ نجح اختبار: {test_name}")
            else:
                logger.error(f"❌ فشل اختبار: {test_name}")
                
        except Exception as e:
            logger.error(f"💥 خطأ في اختبار {test_name}: {e}")
            results[test_name] = False
    
    # تقرير النتائج النهائي
    logger.info(f"\n{'='*50}")
    logger.info("📊 تقرير النتائج النهائي")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        logger.info(f"{status} - {test_name}")
    
    logger.info(f"\n📈 النتيجة الإجمالية: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        logger.info("🎉 جميع الاختبارات نجحت! الإصلاحات تعمل بشكل صحيح.")
    else:
        logger.warning(f"⚠️ {total - passed} اختبارات فشلت. يحتاج المزيد من الإصلاحات.")

if __name__ == "__main__":
    asyncio.run(main())
