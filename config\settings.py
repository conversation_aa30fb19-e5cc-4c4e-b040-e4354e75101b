# إعدادات الوكيل البرمجي لأخبار الألعاب
import os
from typing import List, Dict, Any
from dotenv import load_dotenv
from modules.api_key_manager import ApiKeyManager

load_dotenv()

# --- Get the absolute path of the project root ---
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# --- مدير مفاتيح Google API المركزي ---
# يقرأ قائمة المفاتيح من متغير البيئة ويزيل أي مسافات بيضاء
google_api_keys_str = os.getenv("GOOGLE_API_KEYS_LIST", "")
google_api_keys = [key.strip() for key in google_api_keys_str.split(',') if key.strip()]

# إضافة المفتاح الرئيسي (GEMINI_API_KEY) إلى بداية القائمة إذا كان موجودًا
main_gemini_key = os.getenv("GEMINI_API_KEY")
if main_gemini_key and main_gemini_key not in google_api_keys:
    google_api_keys.insert(0, main_gemini_key)

# إضافة مفتاح YouTube الجديد في المقدمة (أولوية عالية)
youtube_api_key = "AIzaSyAxLImn6q9_UOIS14BQ1qu537uohDlT16Y"
if youtube_api_key not in google_api_keys:
    google_api_keys.insert(0, youtube_api_key)

# إضافة مفاتيح Google Search الجديدة إلى القائمة
new_google_search_keys = [
    # مفتاح YouTube API الجديد (مضاف من المستخدم)
    "AIzaSyAxLImn6q9_UOIS14BQ1qu537uohDlT16Y",

    # المجموعة الأولى
    "AIzaSyAEnnXE2U5ypT-XgxyZUMt8JiMFoa9bgU4",
    "AIzaSyBTjOvcIm_qDBx00U_3FqSlYEQI7kpOIlk",
    "AIzaSyDxvu1Z-DH7HnF_Z0y6OP13astfqeZMdkQ",
    "AIzaSyDfLuY9QV-Gg2LNTzWw7L8olw3sR3_sFDQ",
    "AIzaSyC3ihvVEvFSgaw_MAjcJLBZQno_8L1F-Tk",

    # المجموعة الثانية (الجديدة)
    "AIzaSyCpWpYWqYfG0XiaMvlJlnYGIeNvxVa-gfU",
    "AIzaSyBiq4VsF9RG0v5EDFiQ2YaDnsObx5JK3Ek",
    "AIzaSyDZ4WEJqhlGQZ0EFMt01awZB1rabEFphfU",
    "AIzaSyBYH-8POlNTPdsWwvHQfw6meyz6ikqEjRk",
    "AIzaSyALFblO8iqAWIMTVdiG0EHdy7mU8WkJk9o"
]

# إضافة المفاتيح الجديدة إلى القائمة إذا لم تكن موجودة
for key in new_google_search_keys:
    if key not in google_api_keys:
        google_api_keys.append(key)

# تهيئة مدير المفاتيح فقط إذا كانت هناك مفاتيح متاحة
if google_api_keys:
    google_api_manager = ApiKeyManager(api_keys=google_api_keys, service_name="Google")
else:
    google_api_manager = None

# سيتم تهيئة مدير مفاتيح Google Search بعد تعريف BotConfig
google_search_api_manager = None

# مدير Google Search المتقدم (سيتم تهيئته عند الحاجة)
google_search_manager_advanced = None

def get_google_search_manager():
    """الحصول على مدير Google Search المتقدم"""
    global google_search_manager_advanced

    if google_search_manager_advanced is None:
        try:
            from modules.google_search_manager import GoogleSearchManager

            # استخدام جميع المفاتيح المتاحة
            search_keys = [
                # المجموعة الأولى
                "AIzaSyAEnnXE2U5ypT-XgxyZUMt8JiMFoa9bgU4",
                "AIzaSyBTjOvcIm_qDBx00U_3FqSlYEQI7kpOIlk",
                "AIzaSyDxvu1Z-DH7HnF_Z0y6OP13astfqeZMdkQ",
                "AIzaSyDfLuY9QV-Gg2LNTzWw7L8olw3sR3_sFDQ",
                "AIzaSyC3ihvVEvFSgaw_MAjcJLBZQno_8L1F-Tk",

                # المجموعة الثانية (الجديدة)
                "AIzaSyCpWpYWqYfG0XiaMvlJlnYGIeNvxVa-gfU",
                "AIzaSyBiq4VsF9RG0v5EDFiQ2YaDnsObx5JK3Ek",
                "AIzaSyDZ4WEJqhlGQZ0EFMt01awZB1rabEFphfU",
                "AIzaSyBYH-8POlNTPdsWwvHQfw6meyz6ikqEjRk",
                "AIzaSyALFblO8iqAWIMTVdiG0EHdy7mU8WkJk9o"
            ]

            search_engine_id = os.getenv("GOOGLE_SEARCH_ENGINE_ID", "")

            if search_keys and search_engine_id:
                google_search_manager_advanced = GoogleSearchManager(
                    api_keys=search_keys,
                    search_engine_id=search_engine_id
                )
                print(f"🔍 تم تهيئة مدير Google Search المتقدم بنجاح")
            else:
                print("⚠️ لا يمكن تهيئة مدير Google Search المتقدم - مفاتيح أو معرف محرك البحث مفقود")

        except Exception as e:
            print(f"❌ خطأ في تهيئة مدير Google Search المتقدم: {e}")

    return google_search_manager_advanced

class BotConfig:
    """تكوين البوت الأساسي"""
    
    # معلومات بوت تيليجرام
    TELEGRAM_BOT_TOKEN = "**********************************************"
    TELEGRAM_BOT_USERNAME = "@Sh6jj_bot"
    TELEGRAM_CHANNEL_URL = "https://t.me/Football_news136"
    TELEGRAM_CHANNEL_ID = "@Football_news136"  # سيتم تحديثها حسب معرف القناة الفعلي
    
    # مفاتيح API (يتم إدارتها الآن عبر google_api_manager)
    GEMINI_API_KEY = main_gemini_key # احتفاظ بالمرجع الرئيسي
    BLOGGER_CLIENT_ID = os.getenv("BLOGGER_CLIENT_ID", "")
    BLOGGER_CLIENT_SECRET = os.getenv("BLOGGER_CLIENT_SECRET", "")
    BLOGGER_BLOG_ID = os.getenv("BLOGGER_BLOG_ID", "")
    BLOGGER_CLIENT_SECRET_FILE = os.path.join(PROJECT_ROOT, os.getenv("BLOGGER_CLIENT_SECRET_FILE", "client_secret.json"))
    TXTIFY_API_URL = os.getenv("TXTIFY_API_URL", "")  # رابط خدمة Txtify المستضافة على Hugging Face
    GOOGLE_SEARCH_ENGINE_ID = os.getenv("GOOGLE_SEARCH_ENGINE_ID", "")

    # مفاتيح APIs للأخبار المتقدمة - نظام جديد قوي!
    NEWSAPI_KEY = os.getenv("NEWSAPI_KEY", "")  # NewsAPI.org
    NEWSDATA_KEY = os.getenv("NEWSDATA_KEY", "pub_6a04788f4edc429a8fb798dc3af6a6fb")  # NewsData.io
    THENEWSAPI_KEY = os.getenv("THENEWSAPI_KEY", "")  # TheNewsAPI.com
    GNEWS_KEY = os.getenv("GNEWS_KEY", "")  # GNews.io

    # مفاتيح APIs للبحث المتقدم
    BRAVE_SEARCH_KEY = os.getenv("BRAVE_SEARCH_KEY", "")  # Brave Search API

    # مفاتيح APIs البديلة لـ Google Search
    SERPAPI_KEY = os.getenv("SERPAPI_KEY", "8b221d23f3aa037d438db307927f904933ae3037")  # SerpAPI Key الجديد الأول
    RAPIDAPI_KEY = os.getenv("RAPIDAPI_KEY", "**************************************************")  # RapidAPI Key (احتياطي)
    SERPAPI_RAPIDAPI_HOST = "serpapi.p.rapidapi.com"  # SerpAPI عبر RapidAPI
    BING_SEARCH_KEY = os.getenv("BING_SEARCH_KEY", "")  # Bing Web Search API
    ZENSERP_KEY = os.getenv("ZENSERP_KEY", "")  # Zenserp API



    # مفاتيح Google Search متعددة للتوزيع والاحتياط
    GOOGLE_SEARCH_KEYS = [
        # المجموعة الأولى (المفاتيح السابقة)
        "AIzaSyAEnnXE2U5ypT-XgxyZUMt8JiMFoa9bgU4",
        "AIzaSyBTjOvcIm_qDBx00U_3FqSlYEQI7kpOIlk",
        "AIzaSyDxvu1Z-DH7HnF_Z0y6OP13astfqeZMdkQ",
        "AIzaSyDfLuY9QV-Gg2LNTzWw7L8olw3sR3_sFDQ",
        "AIzaSyC3ihvVEvFSgaw_MAjcJLBZQno_8L1F-Tk",

        # المجموعة الثانية (المفاتيح الجديدة)
        "AIzaSyCpWpYWqYfG0XiaMvlJlnYGIeNvxVa-gfU",
        "AIzaSyBiq4VsF9RG0v5EDFiQ2YaDnsObx5JK3Ek",
        "AIzaSyDZ4WEJqhlGQZ0EFMt01awZB1rabEFphfU",
        "AIzaSyBYH-8POlNTPdsWwvHQfw6meyz6ikqEjRk",
        "AIzaSyALFblO8iqAWIMTVdiG0EHdy7mU8WkJk9o"
    ]

    # المفتاح الافتراضي (للتوافق مع الكود الموجود)
    GOOGLE_SEARCH_KEY = os.getenv("GOOGLE_SEARCH_KEY", GOOGLE_SEARCH_KEYS[0])  # Google Custom Search

    # مفاتيح APIs للبحث العميق باستخدام MCP وخدمات AI مجانية
    PERPLEXITY_API_KEY = os.getenv("PERPLEXITY_API_KEY", "")  # Perplexity AI (5 استعلامات/يوم مجاناً)
    YOU_COM_API_KEY = os.getenv("YOU_COM_API_KEY", "")  # You.com (100 استعلام/يوم مجاناً)
    SERPER_API_KEY = os.getenv("SERPER_API_KEY", "")  # Serper (2500 استعلام/شهر مجاناً)

    # مفاتيح Tavily API للبحث العميق مع الذكاء الاصطناعي
    TAVILY_API_KEYS = [
        os.getenv("TAVILY_API_KEY_1", "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"),  # المفتاح الأول
        os.getenv("TAVILY_API_KEY_2", "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"),  # المفتاح الثاني
    ]
    TAVILY_API_KEY = TAVILY_API_KEYS[0]  # المفتاح الافتراضي للتوافق مع الكود الموجود

    # إعدادات نظام YouTube المتقدم مع Whisper
    YOUTUBE_API_KEY = os.getenv("YOUTUBE_API_KEY", GOOGLE_SEARCH_KEYS[0] if GOOGLE_SEARCH_KEYS else "")  # مفتاح YouTube Data API
    WHISPER_API_URL = os.getenv("WHISPER_API_URL", "https://nanami34-ai55.hf.space/api/transcribe")
    WHISPER_API_KEY = os.getenv("WHISPER_API_KEY", "whisper-hf-spaces-2025")
    HF_TOKEN = os.getenv("HF_TOKEN", "*************************************")

    # إعدادات فلترة الفيديوهات
    MAX_VIDEO_DURATION_MINUTES = int(os.getenv("MAX_VIDEO_DURATION_MINUTES", "30"))  # 30 دقيقة كحد أقصى
    MAX_VIDEO_AGE_DAYS = int(os.getenv("MAX_VIDEO_AGE_DAYS", "60"))  # شهرين كحد أقصى

    # إعدادات نظام الموافقة
    APPROVAL_TIMEOUT_MINUTES = int(os.getenv("APPROVAL_TIMEOUT_MINUTES", "5"))  # 5 دقائق للموافقة
    AUTO_APPROVE_ON_TIMEOUT = os.getenv("AUTO_APPROVE_ON_TIMEOUT", "true").lower() == "true"

    # مفاتيح APIs للصور الآمنة - ImageGuard Pro
    PEXELS_API_KEY = os.getenv("PEXELS_API_KEY", "")  # مفتاح Pexels API (مجاني - 200 طلب/ساعة)
    PIXABAY_API_KEY = os.getenv("PIXABAY_API_KEY", "")  # مفتاح Pixabay API (مجاني - 5000 طلب/شهر)
    UNSPLASH_ACCESS_KEY = os.getenv("UNSPLASH_ACCESS_KEY", "")  # مفتاح Unsplash API (مجاني - 50 طلب/ساعة)

    # مفاتيح APIs لإنشاء الصور بالذكاء الاصطناعي - AI Image Generation
    FREEPIK_API_KEY = os.getenv("FREEPIK_API_KEY", "FPSX1ee910637a8ec349e6d8c7f17a57740b")  # مفتاح Freepik API المحدث
    FLUXAI_API_KEY = os.getenv("FLUXAI_API_KEY", "b6863038ac459a1f8cd9e30d82cdd989")  # مفتاح FluxAI API
    LEONARDO_AI_API_KEY = os.getenv("LEONARDO_AI_API_KEY", "")  # مفتاح Leonardo AI (اختياري)
    MIDJOURNEY_API_KEY = os.getenv("MIDJOURNEY_API_KEY", "")  # مفتاح Midjourney (اختياري)

    # إعدادات التشغيل
    SEARCH_INTERVAL_HOURS = 2  # البحث كل ساعتين
    MAX_RETRIES = 3  # عدد محاولات إعادة التنفيذ
    RETRY_DELAY = 30  # تأخير بين المحاولات (ثانية)
    
    # إعدادات قاعدة البيانات
    DATABASE_PATH = "data/articles.db"
    
    # إعدادات التسجيل
    LOG_LEVEL = "INFO"
    LOG_FILE = "logs/bot.log"
    
    # حدود API
    GEMINI_RATE_LIMIT = 60  # طلبات في الدقيقة
    TELEGRAM_RATE_LIMIT = 30  # رسائل في الثانية
    BLOGGER_RATE_LIMIT = 100  # طلبات في اليوم

    # حدود APIs الصور
    PEXELS_RATE_LIMIT = 200  # طلبات في الساعة
    PIXABAY_RATE_LIMIT = 5000  # طلبات في الشهر
    UNSPLASH_RATE_LIMIT = 50  # طلبات في الساعة

    # حدود APIs إنشاء الصور بالذكاء الاصطناعي
    FREEPIK_RATE_LIMIT = 100  # طلبات في اليوم (حسب الخطة)
    FLUXAI_RATE_LIMIT = 1000  # طلبات في اليوم (مجاني)
    LEONARDO_AI_RATE_LIMIT = 150  # طلبات في اليوم
    MIDJOURNEY_RATE_LIMIT = 25  # طلبات في الساعة

    # حدود APIs الأخبار المتقدمة
    NEWSAPI_RATE_LIMIT = 1000  # طلبات في اليوم (خطة مجانية)
    NEWSDATA_RATE_LIMIT = 200  # طلبات في اليوم (خطة مجانية)
    THENEWSAPI_RATE_LIMIT = 100  # طلبات في اليوم
    GNEWS_RATE_LIMIT = 100  # طلبات في اليوم

    # حدود APIs البحث المتقدم
    BRAVE_SEARCH_RATE_LIMIT = 2000  # طلبات في الشهر (خطة مجانية)
    GOOGLE_SEARCH_RATE_LIMIT = 100  # طلبات في اليوم (خطة مجانية)
    
    @classmethod
    def validate_config(cls) -> bool:
        """التحقق من صحة التكوين"""
        required_fields = [
            "GEMINI_API_KEY",
            "BLOGGER_CLIENT_ID",
            "BLOGGER_CLIENT_SECRET",
            "BLOGGER_BLOG_ID"
        ]

        for field in required_fields:
            if not getattr(cls, field):
                print(f"⚠️ حقل مطلوب غير موجود: {field}")
                return False
        return True

# تهيئة مدير مفاتيح Google Search بعد تعريف BotConfig
try:
    google_search_keys = BotConfig.GOOGLE_SEARCH_KEYS

    if google_search_keys:
        google_search_api_manager = ApiKeyManager(
            api_keys=google_search_keys,
            service_name="Google Search",
            auto_recovery_minutes=30,  # إعادة تفعيل أسرع للبحث
            load_balancing=True
        )
        print(f"🔑 تم تهيئة مدير مفاتيح Google Search مع {len(google_search_keys)} مفتاح")
except Exception as e:
    print(f"⚠️ خطأ في تهيئة مدير مفاتيح Google Search: {e}")
    google_search_api_manager = None

class SourcesConfig:
    """تكوين مصادر البيانات"""

    # المواقع الرسمية
    OFFICIAL_SOURCES = [
        "https://www.gamespot.com",
        "https://www.polygon.com",
        "https://www.kotaku.com",
        "https://www.eurogamer.net",
        "https://www.gamesindustry.biz",
        "https://www.gamedeveloper.com"
    ]

    # مواقع الألعاب المتخصصة
    GAMING_SITES = [
        "https://ign.com",
        "https://commonsensemedia.org",
        "https://pcgamesn.com",
        "https://www.gamesradar.com",
        "https://www.destructoid.com",
        "https://www.rockpapershotgun.com",
        "https://www.pcgamer.com",
        "https://www.gameinformer.com",
        "https://www.theverge.com/games",
        "https://arstechnica.com/gaming"
    ]

    # المواقع العربية
    ARABIC_SITES = [
        "https://vga4a.com",
        "https://www.true-gaming.net",
        "https://saudigamer.com",
        # "https://www.arageek.com/tech/gaming",  # معطل - 404
        # "https://www.tech-wd.com/wd/category/games",  # معطل - 404
        "https://www.i3lam.com/category/games"
    ]

    # مواقع المراجعات
    REVIEW_SITES = [
        "https://www.ign.com/reviews/games",
        "https://www.gamespot.com/reviews/",
        "https://www.metacritic.com/game",
        "https://opencritic.com",
        "https://www.giantbomb.com/reviews"
    ]

    # المنتديات
    FORUM_SITES = [
        # "https://www.resetera.com/forums/gaming-forum.4/",  # معطل - 404
        "https://www.reddit.com/r/gamingnews/",
        # "https://www.neogaf.com/forums/gaming.2/",  # معطل - 403 Forbidden
        "https://www.reddit.com/r/Games/",
        "https://www.reddit.com/r/gaming/"
    ]

    # مصادر الأخبار السريعة
    NEWS_AGGREGATORS = [
        "https://news.google.com/topics/CAAqJggKIiBDQkFTRWdvSUwyMHZNREZqY0hsNUVnVnVaWGR6S0FBUAE",
        "https://www.gamedev.net/news",
        "https://www.gamasutra.com/news"
    ]

    # مصادر التحديثات والإعلانات
    ANNOUNCEMENT_SOURCES = [
        "https://blog.playstation.com",
        "https://news.xbox.com",
        "https://www.nintendo.com/us/whatsnew",
        "https://store.steampowered.com/news",
        "https://blog.epicgames.com"
    ]
    
    # قنوات يوتيوب واستعلامات البحث المحسنة
    YOUTUBE_CHANNELS = [
        "new game trailers 2025",
        "upcoming games 2025",
        "video game news today",
        "أخبار ألعاب الفيديو",
        "game reviews 2025",
        "مراجعات ألعاب جديدة",
        "gameplay footage new",
        "new game releases this week",
        "gaming announcements",
        "indie games 2025",
        "AAA games news",
        "mobile games updates",
        "PC gaming news",
        "console gaming updates",
        "VR games 2025",
        "esports news",
        "gaming industry news",
        "game development updates",
        "sandbox games updates",
        "battle royale news",
        "fps games updates",
        "sports games 2025",
        "open world games news",
        "rpg games updates",
        "indie games news",
        "mobile gaming updates"
    ]
    
    # حسابات تويتر
    TWITTER_ACCOUNTS = [
        "@IGN",
        "@GameSpot",
        "@PlayStation",
        "@Xbox"
    ]
    
    # متاجر التطبيقات
    APP_STORES = [
        "https://play.google.com",
        "https://store.steampowered.com"
    ]

class ContentConfig:
    """تكوين المحتوى"""
    
    # أنواع المحتوى
    CONTENT_TYPES = [
        "أخبار_الألعاب",
        "تحديثات_الألعاب",
        "مراجعات_جديدة",
        "عروض_خاصة",
        "مقالات_رأي"
    ]
    
    # اللهجات المدعومة
    DIALECTS = {
        "standard": "العربية الفصحى",
        "egyptian": "مصرية",
        "saudi": "سعودية"
    }
    DEFAULT_DIALECT = "standard"  # اللهجة الافتراضية
    
    # تنسيقات النشر على تيليجرام
    TELEGRAM_FORMATS = [
        "ملخص_قصير",
        "نقاط_رئيسية", 
        "سؤال_وجواب",
        "اقتباس",
        "صورة_مع_نص"
    ]
    
    # كلمات مفتاحية أساسية محسنة
    BASE_KEYWORDS = [
        "ألعاب الفيديو",
        "video games",
        "أخبار الألعاب",
        "gaming news",
        "تحديثات الألعاب",
        "game updates",
        "إصدارات جديدة",
        "new releases",
        "مراجعات الألعاب",
        "game reviews",
        "ألعاب 2025",
        "games 2025",
        "ألعاب الكمبيوتر",
        "PC games",
        "ألعاب الموبايل",
        "mobile games",
        "ألعاب الكونسول",
        "console games",
        "ألعاب مجانية",
        "free games",
        "ألعاب مدفوعة",
        "paid games",
        "ألعاب أونلاين",
        "online games",
        "ألعاب أوفلاين",
        "offline games"
    ]
    
    # فئات المقالات
    ARTICLE_CATEGORIES = [
        "أخبار الألعاب",
        "تحديثات الألعاب",
        "مراجعات وتحليلات",
        "مقالات رأي",
        "أخبار الشركات"
    ]

class SEOConfig:
    """تكوين تحسين محركات البحث المحسن"""

    # طول العنوان المثالي
    TITLE_LENGTH_MIN = 30
    TITLE_LENGTH_MAX = 60

    # طول الوصف التعريفي
    META_DESCRIPTION_LENGTH = 155

    # كثافة الكلمات المفتاحية
    KEYWORD_DENSITY = 2.0  # نسبة مئوية محسنة

    # عدد الكلمات المفتاحية لكل مقال
    MAX_KEYWORDS_PER_ARTICLE = 15

    # قوالب العناوين الجذابة والمحسنة لـ SEO
    TITLE_TEMPLATES = [
        "🔥 خبر عاجل: {content} - أحدث أخبار الألعاب",
        "⚡ دليل شامل: {content} - كل ما تحتاج معرفته",
        "🎮 مراجعة حصرية: {content} - تقييم مفصل",
        "🚀 تحديث جديد: {content} - آخر التطورات",
        "💎 اكتشف: {content} - أفضل الألعاب الجديدة",
        "🏆 أفضل: {content} - قائمة محدثة 2025",
        "📱 أخبار: {content} - تحديثات يومية",
        "🎯 تحليل: {content} - رؤية عميقة",
        "⭐ مميز: {content} - محتوى حصري",
        "🔍 استكشف: {content} - دليل المبتدئين"
    ]

    # كلمات مفتاحية عالية الأداء
    HIGH_PERFORMANCE_KEYWORDS = [
        "أفضل الألعاب",
        "best games",
        "مراجعة لعبة",
        "game review",
        "تحديث جديد",
        "new update",
        "ألعاب مجانية",
        "free games",
        "نصائح وحيل",
        "tips and tricks",
        "دليل اللعبة",
        "game guide"
    ]

    # عبارات دعوة للعمل
    CALL_TO_ACTION_PHRASES = [
        "شاركنا رأيك في التعليقات",
        "ما رأيكم في هذا التحديث؟",
        "أخبرونا عن تجربتكم مع اللعبة",
        "هل جربتم هذه اللعبة من قبل؟",
        "ما هي لعبتكم المفضلة؟",
        "انتظروا المزيد من المراجعات",
        "تابعونا للحصول على آخر الأخبار",
        "لا تفوتوا أحدث التحديثات"
    ]

class ImageSafetyConfig:
    """تكوين أمان الصور - ImageGuard Pro"""

    # كلمات آمنة للبحث عن الصور
    SAFE_KEYWORDS = [
        'gaming', 'controller', 'console', 'computer', 'technology',
        'esports', 'digital', 'modern', 'setup', 'workspace', 'keyboard',
        'mouse', 'headset', 'monitor', 'screen', 'device', 'electronic'
    ]

    # كلمات محظورة لضمان الامتثال لـ AdSense
    FORBIDDEN_KEYWORDS = [
        'violence', 'blood', 'weapon', 'gun', 'fight', 'war', 'battle',
        'alcohol', 'beer', 'wine', 'cigarette', 'smoking', 'tobacco',
        'gambling', 'casino', 'poker', 'bet', 'adult', 'sexy', 'nude'
    ]

    # أولوية مصادر الصور (من الأفضل للأقل)
    SOURCE_PRIORITY = ['Pexels', 'Pixabay', 'Unsplash']

    # الحد الأدنى لجودة الصور
    MIN_IMAGE_WIDTH = 400
    MIN_IMAGE_HEIGHT = 300

    # الرخص المسموحة
    ALLOWED_LICENSES = [
        'Pexels License',
        'Pixabay License',
        'Unsplash License',
        'Creative Commons CC0'
    ]

    # صور احتياطية آمنة (URLs ثابتة)
    FALLBACK_IMAGES = [
        {
            'url': 'https://images.pexels.com/photos/442576/pexels-photo-442576.jpeg',
            'description': 'Gaming controller on dark background',
            'license': 'Pexels License',
            'attribution': 'Photo by Lucie Liz from Pexels'
        },
        {
            'url': 'https://images.pexels.com/photos/1174746/pexels-photo-1174746.jpeg',
            'description': 'Modern gaming setup with RGB lighting',
            'license': 'Pexels License',
            'attribution': 'Photo by FOX from Pexels'
        },
        {
            'url': 'https://images.pexels.com/photos/735911/pexels-photo-735911.jpeg',
            'description': 'Retro gaming console and controller',
            'license': 'Pexels License',
            'attribution': 'Photo by Garrett Morrow from Pexels'
        },
        {
            'url': 'https://images.pexels.com/photos/194511/pexels-photo-194511.jpeg',
            'description': 'Gaming keyboard with colorful backlighting',
            'license': 'Pexels License',
            'attribution': 'Photo by Lukas from Pexels'
        }
    ]
